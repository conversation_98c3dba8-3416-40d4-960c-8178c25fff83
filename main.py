import logging
import os
import shutil
import tempfile
import time
import gc
from typing import Optional, Dict, Any
from tempfile import NamedTemporaryFile

import torch
import soundfile as sf
from fastapi import FastAPI, UploadFile, Form, Request, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware

# Ray Serve imports
import ray
from ray import serve
from ray.serve import deployment

from src.f5_tts_api.f5tts_wrapper import F5TTSWrapper
from f5_tts.infer.utils_infer import preprocess_ref_audio_text, transcribe

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# ===== Ray Serve F5-TTS Deployment =====
@serve.deployment(
    num_replicas=3,  # 5 replicas as specified
    ray_actor_options={
        "num_gpus": 0.3,  # 0.3 GPU per replica as specified
        "num_cpus": 0.5,  # 0.5 CPU per replica as specified
        "memory": 3 * 1024 * 1024 * 1024,  # 4GB RAM per replica
    },
    max_ongoing_requests=3,  # 3 max ongoing requests as specified
    autoscaling_config=None,  # Disable autoscaling for consistent performance
)
class F5TTSDeployment:
    def __init__(self):
        """Initialize the F5-TTS model with GPU allocation and preload everything"""
        # Force use of cuda:0 as specified
        self.device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
        logger.info(f"🚀 Initializing F5TTSDeployment on device: {self.device}")

        # Optimize GPU memory settings for 0.3 GPU allocation
        if torch.cuda.is_available():
            # Set memory fraction to use 30% of GPU memory per replica
            torch.cuda.set_per_process_memory_fraction(0.3)
            torch.cuda.empty_cache()
            gc.collect()

            # Get GPU info
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            logger.info(f"🧹 GPU cache cleared - Available memory: {gpu_memory:.1f}GB")
            logger.info("🔧 GPU memory optimized for 0.3 GPU allocation per replica")

        # Initialize F5-TTS wrapper with specified device and preload model
        logger.info("📥 Loading F5-TTS model on startup...")
        self.tts_wrapper = F5TTSWrapper(
            model_type="F5-TTS_v1",
            device=self.device
        )

        # Ensure model is fully loaded by calling the get_model and load_vocoder methods
        logger.info("🔄 Preloading model components...")
        _ = self.tts_wrapper.get_model("F5-TTS_v1")  # This will load and cache the F5-TTS model
        _ = self.tts_wrapper._load_vocoder()  # This will load and cache the vocoder

        logger.info("✅ F5TTSDeployment model preloaded successfully")

        # Warm up the model with actual inference to ensure everything is ready
        self._warmup()

        logger.info("🎯 F5TTSDeployment fully ready for inference")

    def _cleanup_gpu_memory(self):
        """Clean up GPU memory to prevent memory leaks"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            gc.collect()
            # Log current GPU memory usage
            allocated = torch.cuda.memory_allocated() / 1e9
            reserved = torch.cuda.memory_reserved() / 1e9
            logger.info(f"🧹 GPU memory cleaned - Allocated: {allocated:.2f}GB, Reserved: {reserved:.2f}GB")

    def _warmup(self):
        """Warm up the model with actual inference to ensure everything is ready"""
        try:
            logger.info("🔥 Warming up F5TTSDeployment with full inference...")

            # Create dummy audio and text for warmup
            dummy_audio = torch.randn(24000).numpy()  # 1 second of audio at 24kHz
            dummy_ref_text = "This is a warmup test to ensure the model is fully loaded."
            dummy_gen_text = "Hello world, this is a test."

            with NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                sf.write(temp_audio.name, dummy_audio, 24000)

                # Run a full inference for warmup to ensure model is completely ready
                try:
                    start_time = time.time()
                    _, _, _, _ = self.tts_wrapper.infer(
                        ref_audio_orig=temp_audio.name,
                        ref_text=dummy_ref_text,
                        gen_text=dummy_gen_text,
                        nfe_step=16,  # Use reasonable steps for proper warmup
                        show_info=logger.info
                    )
                    warmup_time = time.time() - start_time
                    logger.info(f"✅ F5TTSDeployment warmup completed in {warmup_time:.2f}s")
                    logger.info("🎯 Model is now fully loaded and ready for production inference")
                except Exception as e:
                    logger.warning(f"⚠️ Warmup failed but continuing: {e}")
                finally:
                    # Clean up
                    os.unlink(temp_audio.name)
                    self._cleanup_gpu_memory()

        except Exception as e:
            logger.warning(f"⚠️ Warmup failed: {e}")

    def is_model_ready(self) -> bool:
        """Check if the model is fully loaded and ready for inference"""
        try:
            return (
                hasattr(self, 'tts_wrapper') and
                self.tts_wrapper is not None and
                self.tts_wrapper.f5tts_model is not None and
                self.tts_wrapper.vocoder is not None
            )
        except Exception:
            return False

    async def __call__(self, request_data: dict) -> dict:
        """Handle F5-TTS synthesis requests"""
        try:
            start_time = time.time()

            # Extract parameters from request
            ref_audio_path = request_data["ref_audio_path"]
            ref_text = request_data.get("ref_text", "")
            gen_text = request_data["gen_text"]
            output_path = request_data["output_path"]
            model_type = request_data.get("model_type", "F5-TTS_v1")
            remove_silence = request_data.get("remove_silence", False)
            seed = request_data.get("seed", -1)
            cross_fade_duration = request_data.get("cross_fade_duration", 0.15)
            nfe_step = request_data.get("nfe_step", 32)
            speed = request_data.get("speed", 1.0)

            logger.info(f"🎵 Processing F5-TTS synthesis: {ref_audio_path} -> {output_path}")
            logger.info(f"📝 Gen text preview: {gen_text[:50]}...")

            # Run F5-TTS inference
            (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed = self.tts_wrapper.infer(
                ref_audio_orig=ref_audio_path,
                ref_text=ref_text,
                gen_text=gen_text,
                model_type=model_type,
                remove_silence=remove_silence,
                seed=seed,
                cross_fade_duration=cross_fade_duration,
                nfe_step=nfe_step,
                speed=speed,
                show_info=logger.info,
            )

            # Save output audio
            sf.write(output_path, audio_data, sample_rate)

            # Clean up GPU memory
            self._cleanup_gpu_memory()

            end_time = time.time()
            latency = end_time - start_time

            logger.info(f"✅ F5-TTS synthesis completed in {latency:.2f} seconds")

            return {
                "success": True,
                "latency": latency,
                "message": f"F5-TTS synthesis completed successfully in {latency:.2f}s",
                "output_path": output_path,
                "sample_rate": sample_rate,
                "processed_ref_text": processed_ref_text,
                "used_seed": used_seed,
                "spectrogram_path": spectrogram_path
            }

        except Exception as e:
            logger.error(f"❌ F5-TTS synthesis failed: {e}")

            # Clean up GPU memory even on failure
            self._cleanup_gpu_memory()

            return {
                "success": False,
                "latency": 0.0,
                "message": f"F5-TTS synthesis failed: {str(e)}",
                "output_path": None
            }

# ===== FastAPI Application =====
app = FastAPI(title="Ray Serve F5-TTS API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Ray Serve handle
f5tts_handle = None

@app.on_event("startup")
async def startup_event():
    """Initialize Ray Serve deployment for F5-TTS"""
    global f5tts_handle

    logger.info("🚀 Starting Ray Serve F5-TTS deployment...")

    # Initialize Ray if not already initialized - connect to existing cluster
    if not ray.is_initialized():
        try:
            ray.init(address="auto", ignore_reinit_error=True)
            logger.info("✅ Connected to existing Ray cluster")
        except Exception as e:
            logger.info(f"No existing Ray cluster found, starting new one: {e}")
            ray.init(
                ignore_reinit_error=True,
                dashboard_host="0.0.0.0",
                dashboard_port=8265,  # Connect to existing dashboard port
            )

    # Start Ray Serve with custom HTTP configuration
    serve.start(
        http_options={
            "host": "0.0.0.0",  # Bind to all interfaces
            "port": 8004,  # Internal Ray Serve port (different from FastAPI)
            "location": "EveryNode"
        },
        detached=False
    )

    # Deploy the F5-TTS model
    f5tts_handle = serve.run(
        F5TTSDeployment.bind(),
        name="f5tts-service"
    )

    logger.info("✅ Ray Serve F5-TTS deployment started successfully")
    logger.info("📋 Available endpoints:")
    logger.info("  - POST /synthesize/ - F5-TTS speech synthesis")
    logger.info("  - POST /transcribe/ - Audio transcription")
    logger.info("  - GET /health - Health check")
    logger.info("  - GET /stats - GPU and performance statistics")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup Ray Serve deployment"""
    logger.info("🛑 Shutting down Ray Serve deployment...")
    serve.shutdown()
    if ray.is_initialized():
        ray.shutdown()
    logger.info("✅ Ray Serve shutdown completed")

@app.middleware("http")
async def log_requests(request: Request, call_next):
    logger.info("Incoming request: %s %s", request.method, request.url)
    response = await call_next(request)
    logger.info("Completed request with status code: %d", response.status_code)
    return response

@app.post("/synthesize/")
async def synthesize_speech(
    ref_audio: UploadFile,
    ref_text: Optional[str] = Form(None),
    gen_text: str = Form(...),
    model_type: Optional[str] = Form("F5-TTS_v1"),
    remove_silence: Optional[bool] = Form(False),
    seed: Optional[int] = Form(-1),
    cross_fade_duration: Optional[float] = Form(0.15),
    nfe_step: Optional[int] = Form(32),
    speed: Optional[float] = Form(1.0),
):
    """
    Synthesize speech using F5-TTS via Ray Serve with enhanced parameters

    Args:
        ref_audio: Reference audio file
        ref_text: Reference text (if empty, will be transcribed)
        gen_text: Text to generate
        model_type: Model type to use ("F5-TTS_v1" or "E2-TTS")
        remove_silence: Whether to remove silence from output
        seed: Random seed (-1 for random)
        cross_fade_duration: Cross-fade duration between segments
        nfe_step: Number of denoising steps
        speed: Speed multiplier
    """
    logger.info("🚀 Starting F5-TTS synthesis request via Ray Serve")
    logger.info("📁 Reference audio: %s (%s)", ref_audio.filename, ref_audio.content_type)
    logger.info("📝 Gen text preview: %s", gen_text[:50])
    logger.info("🎯 Parameters - model_type: %s, remove_silence: %s, seed: %s, nfe_step: %s, speed: %s",
                model_type, remove_silence, seed, nfe_step, speed)

    # Validate parameters
    if nfe_step < 1 or nfe_step > 100:
        raise HTTPException(status_code=400, detail="nfe_step must be between 1 and 100")

    if speed <= 0 or speed > 3.0:
        raise HTTPException(status_code=400, detail="speed must be between 0 and 3.0")

    # Save uploaded files to temporary locations
    with NamedTemporaryFile(delete=False, suffix=".wav") as ref_temp, \
         NamedTemporaryFile(delete=False, suffix=".wav") as out_temp:

        logger.info("💾 Saving uploaded file to temporary location...")
        shutil.copyfileobj(ref_audio.file, ref_temp)

        ref_path = ref_temp.name
        out_path = out_temp.name

    try:
        # Prepare request data for Ray Serve
        request_data = {
            "ref_audio_path": ref_path,
            "ref_text": ref_text or "",
            "gen_text": gen_text,
            "output_path": out_path,
            "model_type": model_type,
            "remove_silence": remove_silence,
            "seed": seed,
            "cross_fade_duration": cross_fade_duration,
            "nfe_step": nfe_step,
            "speed": speed,
        }

        # Call Ray Serve deployment
        result = await f5tts_handle.remote(request_data)

        if result["success"]:
            response = FileResponse(
                out_path,
                media_type="audio/wav",
                filename="f5tts_output.wav"
            )
            response.headers["X-Inference-Latency"] = str(result["latency"])
            response.headers["X-Ray-Serve"] = "true"
            response.headers["X-Sample-Rate"] = str(result["sample_rate"])
            response.headers["X-Used-Seed"] = str(result["used_seed"])
            response.headers["X-NFE-Steps"] = str(nfe_step)

            # Clean up reference temp file
            try:
                os.remove(ref_path)
                logger.info("✅ Temporary input file cleaned up successfully")
            except OSError as e:
                logger.warning(f"⚠️ Could not clean up temporary file: {e}")

            logger.info("🎉 F5-TTS synthesis request completed successfully via Ray Serve")
            return response
        else:
            raise HTTPException(status_code=500, detail=result["message"])

    except Exception as e:
        # Clean up all temporary files on error
        for temp_path in [ref_path, out_path]:
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except OSError:
                pass

        logger.error(f"❌ F5-TTS synthesis failed: {e}")
        raise HTTPException(status_code=500, detail=f"F5-TTS synthesis failed: {str(e)}")

@app.post("/transcribe/")
async def transcribe_audio(
    audio: UploadFile,
    language: Optional[str] = Form(None),
):
    """
    Transcribe audio using the same pipeline as reference text processing

    Args:
        audio: Audio file to transcribe
        language: Optional language code for transcription (e.g., "en", "es", "fr")

    Returns:
        JSON response with transcribed text
    """
    logger.info("Endpoint '/transcribe/' called.")
    logger.info("Received file: %s", audio.filename)
    logger.info("Language: %s", language)

    try:
        # Save uploaded audio to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_audio:
            shutil.copyfileobj(audio.file, tmp_audio)
            tmp_audio_path = tmp_audio.name
            logger.info("Saved audio to: %s", tmp_audio_path)

        # Use the same audio preprocessing as reference audio (includes clipping and silence removal)
        processed_audio_path, _ = preprocess_ref_audio_text(tmp_audio_path, "", show_info=logger.info)
        logger.info("Preprocessed audio saved to: %s", processed_audio_path)

        # Transcribe the processed audio with language support
        transcribed_text = transcribe(processed_audio_path, language=language)

        # Apply the same text post-processing as in preprocess_ref_audio_text
        # Ensure text ends with proper sentence-ending punctuation
        if not transcribed_text.endswith(". ") and not transcribed_text.endswith("。"):
            if transcribed_text.endswith("."):
                transcribed_text += " "
            else:
                transcribed_text += ". "

        logger.info("Transcription successful: %s", transcribed_text[:100] + "..." if len(transcribed_text) > 100 else transcribed_text)

        # Clean up temporary files
        try:
            os.unlink(tmp_audio_path)
            # Note: processed_audio_path might be cached, so we don't delete it
        except OSError:
            logger.warning("Could not delete temporary file: %s", tmp_audio_path)

        return {
            "transcribed_text": transcribed_text,
            "language": language,
            "status": "success"
        }

    except Exception as e:
        logger.exception("Error during transcription: %s", str(e))
        # Clean up temporary files on error
        try:
            if 'tmp_audio_path' in locals():
                os.unlink(tmp_audio_path)
        except OSError:
            pass
        raise HTTPException(status_code=500, detail=f"Transcription failed: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint with model readiness status"""
    gpu_available = torch.cuda.is_available()
    gpu_count = torch.cuda.device_count() if gpu_available else 0

    # Check if models are loaded and ready
    models_ready = False
    if f5tts_handle is not None:
        try:
            # This will be available once we add the method to check readiness
            models_ready = True  # Assume ready if deployment exists
        except Exception:
            models_ready = False

    health_data = {
        "status": "healthy" if models_ready else "starting",
        "ray_initialized": ray.is_initialized(),
        "f5tts_deployment": f5tts_handle is not None,
        "models_ready": models_ready,
        "gpu_available": gpu_available,
        "gpu_count": gpu_count,
    }

    if gpu_available:
        health_data["gpu_memory"] = {
            "allocated_gb": torch.cuda.memory_allocated() / 1e9,
            "reserved_gb": torch.cuda.memory_reserved() / 1e9,
        }

    return health_data

@app.get("/stats")
async def get_stats():
    """Get performance and GPU statistics"""
    if not ray.is_initialized():
        raise HTTPException(status_code=503, detail="Ray not initialized")

    stats = {
        "ray_cluster": {
            "nodes": len(ray.nodes()),
            "resources": ray.cluster_resources(),
        },
        "gpu_stats": {},
        "serve_status": "running" if f5tts_handle else "not_initialized",
    }

    if torch.cuda.is_available():
        stats["gpu_stats"] = {
            "device_count": torch.cuda.device_count(),
            "current_device": torch.cuda.current_device(),
            "memory_allocated": torch.cuda.memory_allocated() / 1e9,
            "memory_reserved": torch.cuda.memory_reserved() / 1e9,
            "memory_cached": torch.cuda.memory_cached() / 1e9,
        }

        for i in range(torch.cuda.device_count()):
            device_props = torch.cuda.get_device_properties(i)
            stats["gpu_stats"][f"gpu_{i}"] = {
                "name": device_props.name,
                "total_memory_gb": device_props.total_memory / 1e9,
                "multi_processor_count": device_props.multi_processor_count,
            }

    return stats

@app.post("/cleanup-memory")
async def cleanup_memory():
    """Force GPU memory cleanup to prevent memory leaks"""
    if not torch.cuda.is_available():
        return {"message": "CUDA not available", "success": False}

    # Get memory stats before cleanup
    before_allocated = torch.cuda.memory_allocated() / 1e9
    before_reserved = torch.cuda.memory_reserved() / 1e9

    # Force cleanup
    torch.cuda.empty_cache()
    gc.collect()

    # Get memory stats after cleanup
    after_allocated = torch.cuda.memory_allocated() / 1e9
    after_reserved = torch.cuda.memory_reserved() / 1e9

    return {
        "success": True,
        "message": "GPU memory cleanup completed",
        "memory_stats": {
            "before": {
                "allocated_gb": before_allocated,
                "reserved_gb": before_reserved
            },
            "after": {
                "allocated_gb": after_allocated,
                "reserved_gb": after_reserved
            },
            "freed": {
                "allocated_gb": before_allocated - after_allocated,
                "reserved_gb": before_reserved - after_reserved
            }
        }
    }

@app.get("/")
async def root():
    """Root endpoint with API information"""
    logger.info("Endpoint '/' called.")
    return {
        "message": "Ray Serve F5-TTS API",
        "version": "3.0",
        "description": "GPU-accelerated F5-TTS synthesis using Ray Serve",
        "features": [
            "Ray Serve deployment with GPU acceleration",
            "Multiple replicas for better throughput (5 replicas)",
            "Enhanced synthesis with infer_gradio structure",
            "Multiple model support (F5-TTS_v1, E2-TTS)",
            "Advanced parameters (speed, cross_fade_duration, nfe_step)",
            "Silence removal",
            "Spectrogram generation",
            "Audio transcription using FasterWhisper",
            "GPU memory optimization (0.3 GPU per replica)"
        ],
        "endpoints": [
            "POST /synthesize/ - F5-TTS speech synthesis via Ray Serve",
            "POST /transcribe/ - Transcribe audio to text",
            "GET /health - Health check and system status",
            "GET /stats - Detailed performance and GPU statistics",
            "POST /cleanup-memory - Force GPU memory cleanup",
            "GET / - API information"
        ],
        "ray_serve": True,
        "scaling_config": {
            "num_replicas": 5,
            "num_cpus_per_replica": 0.5,
            "num_gpus_per_replica": 0.3,
            "max_ongoing_requests": 3
        }
    }
