#!/usr/bin/env python3
"""
Test script for Ray Serve F5-TTS API

Usage:
    python test_ray_serve_f5tts.py --url http://localhost:8011
    python test_ray_serve_f5tts.py --health-only
"""

import argparse
import requests
import json
import time
import os
import sys
from pathlib import Path

def test_health_endpoint(base_url):
    """Test the health endpoint"""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        response.raise_for_status()
        
        health_data = response.json()
        print("✅ Health check passed")
        print(f"   Status: {health_data.get('status')}")
        print(f"   Ray initialized: {health_data.get('ray_initialized')}")
        print(f"   F5-TTS deployment: {health_data.get('f5tts_deployment')}")
        print(f"   GPU available: {health_data.get('gpu_available')}")
        print(f"   GPU count: {health_data.get('gpu_count')}")
        
        if health_data.get('gpu_memory'):
            gpu_mem = health_data['gpu_memory']
            print(f"   GPU memory allocated: {gpu_mem.get('allocated_gb', 0):.2f}GB")
            print(f"   GPU memory reserved: {gpu_mem.get('reserved_gb', 0):.2f}GB")
        
        return True
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_stats_endpoint(base_url):
    """Test the stats endpoint"""
    print("\n📊 Testing stats endpoint...")
    try:
        response = requests.get(f"{base_url}/stats", timeout=10)
        response.raise_for_status()
        
        stats_data = response.json()
        print("✅ Stats check passed")
        print(f"   Serve status: {stats_data.get('serve_status')}")
        
        if 'ray_cluster' in stats_data:
            cluster = stats_data['ray_cluster']
            print(f"   Ray nodes: {cluster.get('nodes')}")
            print(f"   Ray resources: {cluster.get('resources')}")
        
        if 'gpu_stats' in stats_data:
            gpu_stats = stats_data['gpu_stats']
            print(f"   GPU device count: {gpu_stats.get('device_count')}")
            print(f"   Current device: {gpu_stats.get('current_device')}")
            print(f"   Memory allocated: {gpu_stats.get('memory_allocated', 0):.2f}GB")
            print(f"   Memory reserved: {gpu_stats.get('memory_reserved', 0):.2f}GB")
        
        return True
    except Exception as e:
        print(f"❌ Stats check failed: {e}")
        return False

def test_root_endpoint(base_url):
    """Test the root endpoint"""
    print("\n🏠 Testing root endpoint...")
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        response.raise_for_status()
        
        root_data = response.json()
        print("✅ Root endpoint check passed")
        print(f"   Message: {root_data.get('message')}")
        print(f"   Version: {root_data.get('version')}")
        print(f"   Ray Serve enabled: {root_data.get('ray_serve')}")
        
        if 'scaling_config' in root_data:
            config = root_data['scaling_config']
            print(f"   Scaling config:")
            print(f"     - Replicas: {config.get('num_replicas')}")
            print(f"     - CPUs per replica: {config.get('num_cpus_per_replica')}")
            print(f"     - GPUs per replica: {config.get('num_gpus_per_replica')}")
            print(f"     - Max ongoing requests: {config.get('max_ongoing_requests')}")
        
        return True
    except Exception as e:
        print(f"❌ Root endpoint check failed: {e}")
        return False

def test_synthesis_endpoint(base_url, ref_audio_path=None, gen_text=None):
    """Test the synthesis endpoint"""
    print("\n🎵 Testing synthesis endpoint...")
    
    # Use default test audio if not provided
    if ref_audio_path is None:
        # Look for any audio file in the current directory
        audio_files = list(Path(".").glob("*.wav")) + list(Path(".").glob("*.mp3"))
        if audio_files:
            ref_audio_path = str(audio_files[0])
            print(f"   Using found audio file: {ref_audio_path}")
        else:
            print("⚠️ No audio file provided and none found in current directory")
            print("   Skipping synthesis test")
            return False
    
    if gen_text is None:
        gen_text = "Hello, this is a test of the F5-TTS Ray Serve API. The synthesis should work correctly."
    
    if not os.path.exists(ref_audio_path):
        print(f"❌ Reference audio file not found: {ref_audio_path}")
        return False
    
    try:
        print(f"   Reference audio: {ref_audio_path}")
        print(f"   Generation text: {gen_text[:50]}...")
        
        # Prepare the request
        with open(ref_audio_path, 'rb') as audio_file:
            files = {'ref_audio': audio_file}
            data = {
                'gen_text': gen_text,
                'model_type': 'F5-TTS_v1',
                'nfe_step': 16,  # Reduced for faster testing
                'speed': 1.0,
                'remove_silence': False,
                'seed': 42
            }
            
            print("   Sending synthesis request...")
            start_time = time.time()
            
            response = requests.post(
                f"{base_url}/synthesize/",
                files=files,
                data=data,
                timeout=120  # 2 minutes timeout for synthesis
            )
            
            end_time = time.time()
            latency = end_time - start_time
            
            response.raise_for_status()
            
            # Save the output
            output_path = "test_output.wav"
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ Synthesis test passed")
            print(f"   Latency: {latency:.2f} seconds")
            print(f"   Output saved to: {output_path}")
            
            # Check response headers
            if 'X-Inference-Latency' in response.headers:
                print(f"   Server-reported latency: {response.headers['X-Inference-Latency']}s")
            if 'X-Ray-Serve' in response.headers:
                print(f"   Ray Serve: {response.headers['X-Ray-Serve']}")
            if 'X-Used-Seed' in response.headers:
                print(f"   Used seed: {response.headers['X-Used-Seed']}")
            
            return True
            
    except Exception as e:
        print(f"❌ Synthesis test failed: {e}")
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Test Ray Serve F5-TTS API",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        "--url",
        type=str,
        default="http://localhost:8011",
        help="Base URL of the F5-TTS API (default: http://localhost:8011)"
    )
    
    parser.add_argument(
        "--health-only",
        action="store_true",
        help="Only test health and stats endpoints"
    )
    
    parser.add_argument(
        "--ref-audio",
        type=str,
        help="Path to reference audio file for synthesis test"
    )
    
    parser.add_argument(
        "--gen-text",
        type=str,
        help="Text to generate for synthesis test"
    )
    
    args = parser.parse_args()
    
    print(f"🧪 Testing Ray Serve F5-TTS API at {args.url}")
    print("=" * 60)
    
    # Test basic endpoints
    health_ok = test_health_endpoint(args.url)
    stats_ok = test_stats_endpoint(args.url)
    root_ok = test_root_endpoint(args.url)
    
    synthesis_ok = True
    if not args.health_only:
        synthesis_ok = test_synthesis_endpoint(args.url, args.ref_audio, args.gen_text)
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"   Health endpoint: {'✅ PASS' if health_ok else '❌ FAIL'}")
    print(f"   Stats endpoint: {'✅ PASS' if stats_ok else '❌ FAIL'}")
    print(f"   Root endpoint: {'✅ PASS' if root_ok else '❌ FAIL'}")
    
    if not args.health_only:
        print(f"   Synthesis endpoint: {'✅ PASS' if synthesis_ok else '❌ FAIL'}")
    
    all_passed = health_ok and stats_ok and root_ok and synthesis_ok
    
    if all_passed:
        print("\n🎉 All tests passed! Ray Serve F5-TTS API is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the server logs.")
        sys.exit(1)

if __name__ == "__main__":
    main()
